import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Form, Icon, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { communityApi, opApi } from '@app/api';
import { SearchAndInput } from '@app/components/common';
import { useStore } from 'react-redux';
import { max } from 'lodash';

const AddSelectedCircleModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;
  const store = useStore();
  const { permissions } = store.getState().session;

  const column = [
    {
      title: '序号',
      dataIndex: 'seq',
      width: 80,
      render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
    },
    {
      title: '圈子名称',
      key: 'name',
      dataIndex: 'name',
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          ref_extensions: values.circle_list.map((v: any) => v.id).join(','),
        };
        communityApi
          .saveCircleConfig(parmas)
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={900}
      visible={props.visible}
      title={
        <>
          推荐圈子
          <span
            style={{
              color: '#999',
              fontSize: 12,
            }}
          >
            显示在V7.6及以后版本的潮圈首页-发现潮圈模块、新用户批量推荐加入圈子弹框等地方
          </span>
        </>
      }
      key={props.key}
      onCancel={props.onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
      okButtonProps={{ disabled: !permissions.includes('circle:config_save') }}
      bodyStyle={{
        height: 500,
        maxHeight: 500,
        overflow: 'auto',
      }}
    >
      <Spin spinning={loading}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="圈子" required>
            {getFieldDecorator('circle_list', {
              initialValue: props.records?.circle_list || [],
              rules: [
                {
                  required: true,
                  type: 'array',
                  message: '请选择圈子',
                },
                {
                  max: 50,
                  message: '最多选择50个圈子',
                  type: 'array',
                },
              ],
            })(
              <NewNewsSearchAndInput
                draggable={true}
                max={50}
                func="getCircleList"
                columns={column}
                placeholder="输入名字搜索"
                body={{ enabled: true }}
                order={true}
                addOnTop={false}
                searchKey="keyword"
                indexKey="list"
                apiWithPagination={true}
                selectMap={(record: any) => `${record.name}`}
              />
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddSelectedCircleModal' })(
  forwardRef<any, any>(AddSelectedCircleModal)
);
